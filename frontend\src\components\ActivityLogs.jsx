import React, { useState } from 'react';
import { FileText, Search } from 'lucide-react';

const ActivityLogs = ({ logs, isDarkMode }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showAll, setShowAll] = useState(false);

  const filteredLogs = logs.filter(log =>
    log.window_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.process_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.hostname?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const displayLogs = showAll ? filteredLogs : filteredLogs.slice(0, 20);

  const formatMemory = (mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  return (
    <div className={`rounded-xl border p-6 shadow-xl ${isDarkMode ? 'bg-white/10 backdrop-blur-sm border-white/20' : 'bg-white border-gray-200'}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-gray-500/20 p-2 rounded-lg border border-gray-500/30">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">Activity Logs</h2>
        </div>
        <div className="text-sm text-slate-300 bg-white/10 px-3 py-1 rounded-full border border-white/20">
          {filteredLogs.length} of {logs.length} logs
        </div>
      </div>

      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <input
            type="text"
            placeholder="Search logs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-slate-400"
          />
        </div>
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {displayLogs.length > 0 ? (
          displayLogs.map((log, index) => (
            <div key={log.id || index} className="border-l-4 border-blue-400 bg-white/5 p-4 rounded-r-lg border border-white/10 hover:bg-white/10 transition-all duration-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs text-slate-400">
                      {new Date(log.timestamp).toLocaleString()}
                    </span>
                    <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded border border-blue-500/30">
                      {log.process_name || 'Unknown'}
                    </span>
                  </div>

                  <h3 className="font-medium text-white mb-1">
                    {log.window_title || 'Unknown Window'}
                  </h3>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>👤 {log.username}@{log.hostname}</span>
                    {log.memory_usage && (
                      <span>💾 {formatMemory(parseInt(log.memory_usage))}</span>
                    )}
                    {log.cpu_usage && (
                      <span>🔥 {log.cpu_usage}%</span>
                    )}
                    {log.idle_time && (
                      <span>😴 {log.idle_time}s idle</span>
                    )}
                  </div>
                  
                  {log.screen_resolution && (
                    <div className="text-xs text-gray-500 mt-1">
                      📺 {log.screen_resolution}
                    </div>
                  )}
                </div>
                
                {log.screenshot_path && (
                  <div className="ml-4">
                    <a
                      href={`http://localhost:5500/${log.screenshot_path}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      📸 View
                    </a>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p>No activity logs found</p>
            {searchTerm && <p className="text-sm">Try adjusting your search terms</p>}
          </div>
        )}
      </div>

      {filteredLogs.length > 20 && !showAll && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(true)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Show all {filteredLogs.length} logs
          </button>
        </div>
      )}

      {showAll && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(false)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Show less
          </button>
        </div>
      )}
    </div>
  );
};

export default ActivityLogs;