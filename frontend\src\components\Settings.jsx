import React, { useState, useEffect } from 'react';
import { Settings as SettingsIcon, Save, RefreshCw } from 'lucide-react';
import axios from 'axios';

const Settings = ({ isDarkMode }) => {
  const [settings, setSettings] = useState({
    screenshot_interval: 60,
    monitoring_interval: 10,
    screenshot_enabled: true
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const loadSettings = async () => {
    try {
      const response = await axios.get('http://localhost:5500/api/settings');
      setSettings(response.data);
    } catch (err) {
      setMessage('Error loading settings: ' + err.message);
    }
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      await axios.post('http://localhost:5500/api/settings', settings);
      setMessage('✅ Settings saved successfully! Restart client to apply changes.');
      setTimeout(() => setMessage(''), 3000);
    } catch (err) {
      setMessage('❌ Error saving settings: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const handleChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className={`rounded-xl border p-6 shadow-xl ${isDarkMode ? 'bg-white/10 backdrop-blur-sm border-white/20' : 'bg-white border-gray-200'}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-slate-500/20 p-2 rounded-lg border border-slate-500/30">
            <SettingsIcon className="h-5 w-5 text-slate-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white">System Settings</h2>
            <p className="text-sm text-slate-300">Configure monitoring parameters</p>
          </div>
        </div>
        <button
          onClick={loadSettings}
          className="text-slate-400 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-colors border border-white/20"
        >
          <RefreshCw className="h-4 w-4" />
        </button>
      </div>

      <div className="space-y-6">
        <div className="bg-white/5 p-4 rounded-lg border border-white/10">
          <label className="block text-sm font-semibold text-white mb-3">
            Screenshot Interval
          </label>
          <div className="flex items-center space-x-3">
            <input
              type="number"
              min="10"
              max="3600"
              value={settings.screenshot_interval}
              onChange={(e) => handleChange('screenshot_interval', parseInt(e.target.value))}
              className="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-white placeholder-slate-400"
            />
            <span className="text-sm font-medium text-slate-300 min-w-0">seconds</span>
          </div>
          <p className="text-xs text-slate-400 mt-2">
            📸 How often to capture screenshots (10 seconds to 1 hour)
          </p>
          <div className="mt-2 text-xs text-blue-400 font-medium">
            Current: Every {Math.floor(settings.screenshot_interval / 60) > 0 ? `${Math.floor(settings.screenshot_interval / 60)}m ${settings.screenshot_interval % 60}s` : `${settings.screenshot_interval}s`}
          </div>
        </div>

        <div className="bg-white/5 p-4 rounded-lg border border-white/10">
          <label className="block text-sm font-semibold text-white mb-3">
            Monitoring Interval
          </label>
          <div className="flex items-center space-x-3">
            <input
              type="number"
              min="5"
              max="60"
              value={settings.monitoring_interval}
              onChange={(e) => handleChange('monitoring_interval', parseInt(e.target.value))}
              className="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-white placeholder-slate-400"
            />
            <span className="text-sm font-medium text-slate-300 min-w-0">seconds</span>
          </div>
          <p className="text-xs text-slate-400 mt-2">
            📊 How often to collect system data (5 to 60 seconds)
          </p>
        </div>

        <div className="bg-white/5 p-4 rounded-lg border border-white/10">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.screenshot_enabled}
              onChange={(e) => handleChange('screenshot_enabled', e.target.checked)}
              className="w-4 h-4 rounded border-white/20 text-blue-600 focus:ring-blue-500 bg-white/10"
            />
            <div>
              <span className="text-sm font-semibold text-white">Enable Screenshots</span>
              <p className="text-xs text-slate-400 mt-1">
                🔄 Turn screenshot capture on/off
              </p>
            </div>
          </label>
        </div>
      </div>

      <div className="mt-8 pt-6 border-t border-white/20">
        <button
          onClick={saveSettings}
          disabled={loading}
          className="w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm border border-blue-500/30"
        >
          <Save className="h-4 w-4" />
          <span>{loading ? 'Saving Settings...' : 'Save Settings'}</span>
        </button>
      </div>

      {message && (
        <div className={`mt-4 p-4 rounded-lg text-sm border ${
          message.includes('✅')
            ? 'bg-green-50 text-green-700 border-green-200'
            : 'bg-red-50 text-red-700 border-red-200'
        }`}>
          <div className="flex items-center space-x-2">
            <div className="flex-shrink-0">
              {message.includes('✅') ? (
                <svg className="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <span className="font-medium">{message}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;