@echo off
title System Monitor - Stop All Services
color 0C

echo.
echo  ███████╗████████╗ ██████╗ ██████╗ 
echo  ██╔════╝╚══██╔══╝██╔═══██╗██╔══██╗
echo  ███████╗   ██║   ██║   ██║██████╔╝
echo  ╚════██║   ██║   ██║   ██║██╔═══╝ 
echo  ███████║   ██║   ╚██████╔╝██║     
echo  ╚══════╝   ╚═╝    ╚═════╝ ╚═╝     
echo.
echo           STOPPING ALL SERVICES...
echo  ================================
echo.

echo [STOP] Killing Node.js processes (Backend)...
taskkill /f /im node.exe >nul 2>&1

echo [STOP] Killing Python processes (Frontend)...
taskkill /f /im python.exe >nul 2>&1

echo [STOP] Killing SystemMonitor client...
taskkill /f /im SystemMonitor.exe >nul 2>&1

echo [STOP] Killing any remaining HTTP servers...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5500" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":6500" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1

echo.
echo ✅ All System Monitor services stopped!
echo.
pause