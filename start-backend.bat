@echo off
echo ========================================
echo    Starting Backend Server
echo ========================================
echo.

cd backend

REM Check if Node.js is installed
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js not found. Please install Node.js first.
    echo Download from: https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing backend dependencies...
    npm install
    echo.
)

echo Starting backend server...
echo.
echo 🚀 Backend API will be available at: http://localhost:5500
echo 🔌 API endpoints:
echo    POST http://localhost:5500/api/logs
echo    GET  http://localhost:5500/api/logs
echo    GET  http://localhost:5500/health
echo.
echo Press Ctrl+C to stop the server
echo.

node server.js

pause