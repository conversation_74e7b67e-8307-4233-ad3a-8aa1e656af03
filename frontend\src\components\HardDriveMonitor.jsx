import React from 'react';
import { HardDrive } from 'lucide-react';

const HardDriveMonitor = ({ logs, isDarkMode }) => {
  // Get real disk usage data from logs
  const latestLog = logs[0] || {};
  const diskUsage = parseFloat(latestLog.disk_usage_percent) || 0;
  const totalSpace = parseInt(latestLog.disk_total_space) || 0;
  const freeSpace = parseInt(latestLog.disk_free_space) || 0;
  const usedSpace = totalSpace - freeSpace;

  const getDiskColor = (usage) => {
    if (usage < 50) return 'text-green-400';
    if (usage < 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  const recentProcesses = logs.slice(0, 5).map(log => log.process_name).filter(Boolean);
  const uniqueProcesses = [...new Set(recentProcesses)];

  return (
    <div className={`rounded-xl border p-6 shadow-xl ${isDarkMode ? 'bg-white/10 backdrop-blur-sm border-white/20' : 'bg-white border-gray-200'}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-blue-500/20 p-2 rounded-lg border border-blue-500/30">
            <HardDrive className="h-6 w-6 text-blue-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">Storage</h2>
        </div>
        <div className={`text-3xl font-bold ${getDiskColor(diskUsage)}`}>
          {diskUsage}%
        </div>
      </div>

      <div className="mb-6">
        <div className="flex justify-between text-sm text-slate-300 mb-3">
          <span>Used: {usedSpace} GB</span>
          <span>Free: {freeSpace} GB</span>
        </div>
        <div className="w-full bg-white/10 rounded-full h-4 border border-white/20">
          <div
            className={`h-4 rounded-full transition-all duration-500 ${
              diskUsage < 50 ? 'bg-gradient-to-r from-green-500 to-green-400' :
              diskUsage < 80 ? 'bg-gradient-to-r from-yellow-500 to-yellow-400' : 'bg-gradient-to-r from-red-500 to-red-400'
            }`}
            style={{ width: `${diskUsage}%` }}
          ></div>
        </div>
      </div>

      <div className="space-y-3 mb-6">
        <h3 className="text-sm font-medium text-white">Recent Activity</h3>
        <div className="space-y-2">
          {uniqueProcesses.slice(0, 3).map((process, index) => (
            <div key={index} className="flex items-center justify-between text-sm bg-white/5 p-2 rounded-lg border border-white/10">
              <span className="text-slate-300">{process || 'Unknown'}</span>
              <span className="text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full border border-blue-500/30">Active</span>
            </div>
          ))}
          {uniqueProcesses.length === 0 && (
            <div className="text-sm text-slate-400 text-center py-4">No recent activity</div>
          )}
        </div>
      </div>

      <div className="pt-4 border-t border-white/20">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="bg-white/5 p-3 rounded-lg border border-white/10">
            <div className="text-lg font-semibold text-white">{totalSpace} GB</div>
            <div className="text-xs text-slate-400">Total Space</div>
          </div>
          <div className="bg-white/5 p-3 rounded-lg border border-white/10">
            <div className="text-lg font-semibold text-white">{uniqueProcesses.length}</div>
            <div className="text-xs text-slate-400">Active Processes</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HardDriveMonitor;