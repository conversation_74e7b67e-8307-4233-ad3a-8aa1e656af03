import React from 'react';
import { HardDrive } from 'lucide-react';

const HardDriveMonitor = ({ logs }) => {
  // Since we don't have actual hard drive data yet, we'll simulate it
  const diskUsage = 65; // Simulated disk usage percentage
  const totalSpace = 500; // GB
  const usedSpace = Math.round((diskUsage / 100) * totalSpace);
  const freeSpace = totalSpace - usedSpace;

  const getDiskColor = (usage) => {
    if (usage < 50) return 'text-green-600';
    if (usage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const recentProcesses = logs.slice(0, 5).map(log => log.process_name).filter(Boolean);
  const uniqueProcesses = [...new Set(recentProcesses)];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <HardDrive className="h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-semibold text-gray-800">Storage</h2>
        </div>
        <div className={`text-2xl font-bold ${getDiskColor(diskUsage)}`}>
          {diskUsage}%
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Used: {usedSpace} GB</span>
          <span>Free: {freeSpace} GB</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${
              diskUsage < 50 ? 'bg-green-500' : 
              diskUsage < 80 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${diskUsage}%` }}
          ></div>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-sm font-medium text-gray-700">Recent Activity</h3>
        <div className="space-y-1">
          {uniqueProcesses.slice(0, 3).map((process, index) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <span className="text-gray-600">{process || 'Unknown'}</span>
              <span className="text-blue-600">Active</span>
            </div>
          ))}
          {uniqueProcesses.length === 0 && (
            <div className="text-sm text-gray-500">No recent activity</div>
          )}
        </div>
      </div>

      <div className="mt-4 pt-4 border-t">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-800">{totalSpace} GB</div>
            <div className="text-xs text-gray-500">Total Space</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-800">{uniqueProcesses.length}</div>
            <div className="text-xs text-gray-500">Active Processes</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HardDriveMonitor;