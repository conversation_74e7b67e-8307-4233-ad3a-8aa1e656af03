@echo off
title System Monitor - One Click Setup
color 0A

echo.
echo  ███████╗██╗   ██╗███████╗████████╗███████╗███╗   ███╗
echo  ██╔════╝╚██╗ ██╔╝██╔════╝╚══██╔══╝██╔════╝████╗ ████║
echo  ███████╗ ╚████╔╝ ███████╗   ██║   █████╗  ██╔████╔██║
echo  ╚════██║  ╚██╔╝  ╚════██║   ██║   ██╔══╝  ██║╚██╔╝██║
echo  ███████║   ██║   ███████║   ██║   ███████╗██║ ╚═╝ ██║
echo  ╚══════╝   ╚═╝   ╚══════╝   ╚═╝   ╚══════╝╚═╝     ╚═╝
echo.
echo                    MONITOR - ONE CLICK SETUP
echo  ================================================================
echo.

REM Check if this is first run
if not exist "setup_done.flag" (
    echo [SETUP] First time setup starting...
    goto :setup
) else (
    echo [INFO] Setup already completed. Starting services...
    goto :start_services
)

:setup
echo.
echo [1/4] Installing backend dependencies...
cd backend
if not exist "node_modules" (
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Failed to install backend dependencies
        pause
        exit /b 1
    )
)
cd ..

echo.
echo [2/4] Building monitoring client...
call client-installer.bat >nul 2>&1

echo.
echo [3/4] Checking database connection...
REM You can add database check here if needed

echo.
echo [4/4] Creating setup flag...
echo Setup completed on %date% %time% > setup_done.flag

echo.
echo [SUCCESS] Setup completed successfully!
echo.

:start_services
echo [SERVICES] Starting all services...
echo.

REM Start backend in new window
echo [BACKEND] Starting backend server on port 5500...
start "Backend Server" cmd /c "cd backend && node server.js"
timeout /t 3 >nul

REM Start frontend in new window  
echo [FRONTEND] Starting frontend dashboard on port 6500...
start "Frontend Dashboard" cmd /c "cd frontend && python -m http.server 6500 || npx http-server -p 6500"
timeout /t 3 >nul

REM Start client if exists
if exist "client\SystemMonitor.exe" (
    echo [CLIENT] Starting monitoring client...
    start "System Monitor Client" cmd /c "cd client && SystemMonitor.exe"
    timeout /t 2 >nul
) else (
    echo [WARNING] Client not found. Run client-installer.bat first.
)

echo.
echo  ================================================================
echo                        🚀 ALL SERVICES STARTED!
echo  ================================================================
echo.
echo  📊 Frontend Dashboard: http://localhost:6500
echo  🔌 Backend API:        http://localhost:5500
echo  🖥️  Client Monitor:     Running in background
echo.
echo  ================================================================
echo.
echo [INFO] All services are running in separate windows.
echo [INFO] Close those windows to stop the services.
echo.
echo Press any key to open dashboard in browser...
pause >nul

REM Open dashboard in default browser
start http://localhost:6500

echo.
echo [DONE] System Monitor is now running!
echo.
pause