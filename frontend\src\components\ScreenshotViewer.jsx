import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Camera, Eye, X } from 'lucide-react';

const ScreenshotViewer = ({ latestLog }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const hasScreenshot = latestLog?.screenshot_path;
  const screenshotUrl = hasScreenshot ? `http://localhost:5500/${latestLog.screenshot_path}` : null;
  
  // Test with a placeholder image if no screenshot
  const testImageUrl = screenshotUrl || 'https://via.placeholder.com/1920x1080/333333/ffffff?text=Test+Screenshot';

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isModalOpen) {
        setIsModalOpen(false);
      }
    };

    if (isModalOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen]);

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 p-6 shadow-xl">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-indigo-500/20 p-2 rounded-lg border border-indigo-500/30">
            <Camera className="h-6 w-6 text-indigo-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">Latest Screenshot</h2>
        </div>
        {hasScreenshot && (
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex items-center space-x-2 text-indigo-400 hover:text-indigo-300 transition-colors bg-white/10 px-3 py-2 rounded-lg border border-white/20 hover:bg-white/20"
          >
            <Eye className="h-4 w-4" />
            <span className="text-sm">View Full</span>
          </button>
        )}
      </div>

      <div className="relative">
        {hasScreenshot ? (
          <div className="space-y-4">
            {/* Debug info */}
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
              <p>Screenshot URL: {screenshotUrl}</p>
              <p>Path: {latestLog.screenshot_path}</p>
              <p>Modal Open: {isModalOpen ? 'Yes' : 'No'}</p>
              <p>Has Screenshot: {hasScreenshot ? 'Yes' : 'No'}</p>
              <div className="flex gap-2 mt-2">
                <button 
                  onClick={() => {
                    console.log('Opening modal...', { screenshotUrl, hasScreenshot });
                    setIsModalOpen(true);
                  }}
                  className="bg-blue-500 text-white px-3 py-1 rounded text-xs"
                >
                  Test Full Screen Modal
                </button>
                <button 
                  onClick={() => window.open(screenshotUrl, '_blank')}
                  className="bg-green-500 text-white px-3 py-1 rounded text-xs"
                >
                  Open in New Tab
                </button>
              </div>
            </div>

            <div className="relative bg-gray-100 rounded-lg overflow-hidden group">
              <img
                src={screenshotUrl}
                alt="Latest Screenshot"
                className="w-full h-48 object-cover cursor-pointer hover:opacity-90 transition-all duration-300 group-hover:scale-105"
                onClick={() => setIsModalOpen(true)}
                onError={(e) => {
                  console.error('Screenshot failed to load:', screenshotUrl);
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
                onLoad={() => {
                  console.log('Screenshot loaded successfully:', screenshotUrl);
                }}
              />
              <div className="hidden items-center justify-center h-48 text-gray-500">
                <div className="text-center">
                  <Camera className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>Screenshot not available</p>
                  <p className="text-xs mt-1">URL: {screenshotUrl}</p>
                </div>
              </div>

              {/* Overlay on hover */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Eye className="h-8 w-8 text-white" />
                </div>
              </div>

              {/* Click hint */}
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                Click to enlarge
              </div>
            </div>

            {/* Direct link for testing */}
            <div className="text-center">
              <a
                href={screenshotUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Open screenshot in new tab
              </a>
            </div>

            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Captured:</span>
                <span>{new Date(latestLog.timestamp).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Resolution:</span>
                <span>{latestLog.screen_resolution || 'Unknown'}</span>
              </div>
              <div className="flex justify-between">
                <span>Active Window:</span>
                <span className="truncate ml-2">{latestLog.window_title || 'Unknown'}</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <Camera className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">No Screenshots Available</p>
            <p className="text-sm">Start the monitoring client to capture screenshots</p>
          </div>
        )}
      </div>

      {/* Full Screen Modal using Portal */}
      {isModalOpen && hasScreenshot && createPortal(
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(0, 0, 0, 0.98)',
            zIndex: 999999,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer'
          }}
          onClick={() => setIsModalOpen(false)}
        >
          {/* Close Button */}
          <button
            onClick={() => setIsModalOpen(false)}
            style={{
              position: 'absolute',
              top: '30px',
              right: '30px',
              zIndex: 1000000,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              borderRadius: '50%',
              width: '50px',
              height: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: 'white',
              fontSize: '24px',
              fontWeight: 'bold'
            }}
          >
            ✕
          </button>

          {/* Screenshot */}
          <img
            src={testImageUrl}
            alt="Full Screenshot"
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              objectFit: 'contain',
              borderRadius: '10px',
              boxShadow: '0 0 50px rgba(0, 0, 0, 0.8)',
              cursor: 'default'
            }}
            onClick={(e) => e.stopPropagation()}
            onError={(e) => {
              console.error('Modal image failed to load:', testImageUrl);
            }}
            onLoad={() => {
              console.log('Modal image loaded successfully:', testImageUrl);
            }}
          />

          {/* Info Panel */}
          <div
            style={{
              position: 'absolute',
              bottom: '30px',
              left: '30px',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              padding: '20px',
              borderRadius: '10px',
              maxWidth: '400px',
              fontSize: '14px'
            }}
          >
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
              📅 {new Date(latestLog.timestamp).toLocaleString()}
            </div>
            <div style={{ marginBottom: '8px' }}>
              🪟 {latestLog.window_title}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              📺 {latestLog.screen_resolution} • 👤 {latestLog.username}@{latestLog.hostname}
            </div>
          </div>

          {/* Instructions */}
          <div
            style={{
              position: 'absolute',
              top: '30px',
              left: '30px',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              padding: '15px',
              borderRadius: '8px',
              fontSize: '12px'
            }}
          >
            Press ESC or click outside to close
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default ScreenshotViewer;