import React from 'react';
import { MemoryStick } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const RAMMonitor = ({ logs }) => {
  const ramData = logs.slice(0, 10).reverse().map((log, index) => ({
    time: new Date(log.timestamp).toLocaleTimeString(),
    memory: parseInt(log.memory_usage) || 0
  }));

  const currentRAM = parseInt(logs[0]?.memory_usage) || 0;
  const maxRAM = Math.max(...logs.slice(0, 10).map(log => parseInt(log.memory_usage) || 0), 0);
  const avgRAM = logs.length > 0 ? 
    Math.round(logs.slice(0, 10).reduce((sum, log) => sum + (parseInt(log.memory_usage) || 0), 0) / Math.min(logs.length, 10)) : 0;

  const formatMemory = (mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  const getRAMColor = (usage) => {
    if (usage < 512) return 'text-green-600';
    if (usage < 1024) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <MemoryStick className="h-6 w-6 text-green-500" />
          <h2 className="text-xl font-semibold text-gray-800">RAM Usage</h2>
        </div>
        <div className={`text-2xl font-bold ${getRAMColor(currentRAM)}`}>
          {formatMemory(currentRAM)}
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Current: {formatMemory(currentRAM)}</span>
          <span>Peak: {formatMemory(maxRAM)}</span>
        </div>
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Average: {formatMemory(avgRAM)}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${
              currentRAM < 512 ? 'bg-green-500' : 
              currentRAM < 1024 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${Math.min((currentRAM / 2048) * 100, 100)}%` }}
          ></div>
        </div>
      </div>

      <div className="h-32">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={ramData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" fontSize={10} />
            <YAxis fontSize={10} />
            <Tooltip formatter={(value) => [formatMemory(value), 'Memory']} />
            <Line 
              type="monotone" 
              dataKey="memory" 
              stroke="#10b981" 
              strokeWidth={2}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default RAMMonitor;