import React from 'react';
import { MemoryStick } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const RAMMonitor = ({ logs, isDarkMode }) => {
  const ramData = logs.slice(0, 10).reverse().map((log, index) => ({
    time: new Date(log.timestamp).toLocaleTimeString(),
    memory: parseInt(log.memory_usage) || 0
  }));

  const currentRAM = parseInt(logs[0]?.memory_usage) || 0;
  const maxRAM = Math.max(...logs.slice(0, 10).map(log => parseInt(log.memory_usage) || 0), 0);
  const avgRAM = logs.length > 0 ? 
    Math.round(logs.slice(0, 10).reduce((sum, log) => sum + (parseInt(log.memory_usage) || 0), 0) / Math.min(logs.length, 10)) : 0;

  const formatMemory = (mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  const getRAMColor = (usage) => {
    if (usage < 512) return 'text-green-400';
    if (usage < 1024) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className={`rounded-xl border p-6 shadow-xl ${isDarkMode ? 'bg-white/10 backdrop-blur-sm border-white/20' : 'bg-white border-gray-200'}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-green-500/20 p-2 rounded-lg border border-green-500/30">
            <MemoryStick className="h-6 w-6 text-green-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">RAM Usage</h2>
        </div>
        <div className={`text-3xl font-bold ${getRAMColor(currentRAM)}`}>
          {formatMemory(currentRAM)}
        </div>
      </div>

      <div className="mb-6">
        <div className="flex justify-between text-sm text-slate-300 mb-2">
          <span>Current: {formatMemory(currentRAM)}</span>
          <span>Peak: {formatMemory(maxRAM)}</span>
        </div>
        <div className="flex justify-between text-sm text-slate-300 mb-3">
          <span>Average: {formatMemory(avgRAM)}</span>
        </div>
        <div className="w-full bg-white/10 rounded-full h-4 border border-white/20">
          <div
            className={`h-4 rounded-full transition-all duration-500 ${
              currentRAM < 512 ? 'bg-gradient-to-r from-green-500 to-green-400' :
              currentRAM < 1024 ? 'bg-gradient-to-r from-yellow-500 to-yellow-400' : 'bg-gradient-to-r from-red-500 to-red-400'
            }`}
            style={{ width: `${Math.min((currentRAM / 2048) * 100, 100)}%` }}
          ></div>
        </div>
      </div>

      <div className="h-40 bg-white/5 rounded-lg p-2 border border-white/10">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={ramData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#ffffff20" />
            <XAxis dataKey="time" fontSize={10} stroke="#94a3b8" />
            <YAxis fontSize={10} stroke="#94a3b8" />
            <Tooltip
              formatter={(value) => [formatMemory(value), 'Memory']}
              contentStyle={{
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: '#fff'
              }}
            />
            <Line
              type="monotone"
              dataKey="memory"
              stroke="#10b981"
              strokeWidth={3}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, fill: '#10b981' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default RAMMonitor;