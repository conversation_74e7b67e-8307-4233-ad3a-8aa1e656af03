@echo off
echo ========================================
echo    System Monitor Client Installer
echo ========================================
echo.

REM Create client directory
if not exist "client" mkdir client
cd client

REM Create the enhanced monitoring client
echo Creating enhanced monitoring client...
(
echo using System;
echo using System.Diagnostics;
echo using System.Drawing;
echo using System.Drawing.Imaging;
echo using System.IO;
echo using System.Net;
echo using System.Runtime.InteropServices;
echo using System.Text;
echo using System.Threading;
echo using System.Web.Script.Serialization;
echo using System.Windows.Forms;
echo.
echo class SystemMonitor
echo {
echo     [DllImport("user32.dll"^)]
echo     static extern IntPtr GetForegroundWindow(^);
echo.
echo     [DllImport("user32.dll"^)]
echo     static extern int GetWindowText(IntPtr hWnd, StringBuilder text, int count^);
echo.
echo     [DllImport("user32.dll"^)]
echo     static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId^);
echo.
echo     [DllImport("user32.dll"^)]
echo     static extern bool GetLastInputInfo(ref LASTINPUTINFO plii^);
echo.
echo     [StructLayout(LayoutKind.Sequential^)]
echo     struct LASTINPUTINFO
echo     {
echo         public static readonly int SizeOf = Marshal.SizeOf(typeof(LASTINPUTINFO^)^);
echo         [MarshalAs(UnmanagedType.U4^)]
echo         public UInt32 cbSize;
echo         [MarshalAs(UnmanagedType.U4^)]
echo         public UInt32 dwTime;
echo     }
echo.
echo     private static readonly string serverUrl = "http://localhost:5500/api/logs";
echo     private static readonly string settingsUrl = "http://localhost:5500/api/settings";
echo     private static int keyStrokes = 0;
echo     private static int mouseClicks = 0;
echo     private static int screenshotInterval = 60; // Default 1 minute
echo     private static int monitoringInterval = 10; // Default 10 seconds
echo     private static int screenshotCounter = 0;
echo.
echo     static void Main(string[] args^)
echo     {
echo         Console.WriteLine("🖥️  Enhanced System Monitor Started"^);
echo         Console.WriteLine("📊 Monitoring: Windows, CPU, Memory, Screenshots"^);
echo         Console.WriteLine("⏹️  Press Ctrl+C to stop"^);
echo         Console.WriteLine("================================"^);
echo.
echo         // Create screenshots directory
echo         if (!Directory.Exists("screenshots"^)^)
echo             Directory.CreateDirectory("screenshots"^);
echo.
echo         // Get initial settings
echo         LoadSettings(^);
echo         int settingsRefreshCounter = 0;
echo.
echo         while (true^)
echo         {
echo             try
echo             {
echo                 // Refresh settings every 30 monitoring cycles (5 minutes if monitoring is 10s^)
echo                 settingsRefreshCounter++;
echo                 if (settingsRefreshCounter >= 30^)
echo                 {
echo                     LoadSettings(^);
echo                     settingsRefreshCounter = 0;
echo                 }
echo.
echo                 LogSystemActivity(^);
echo                 Thread.Sleep(monitoringInterval * 1000^); // Use configurable interval
echo             }
echo             catch (Exception ex^)
echo             {
echo                 Console.WriteLine("❌ Error: " + ex.Message^);
echo                 Thread.Sleep(5000^);
echo             }
echo         }
echo     }
echo.
echo     static void LogSystemActivity(^)
echo     {
echo         IntPtr handle = GetForegroundWindow(^);
echo         StringBuilder windowTitle = new StringBuilder(256^);
echo         GetWindowText(handle, windowTitle, windowTitle.Capacity^);
echo.
echo         if (string.IsNullOrEmpty(windowTitle.ToString(^)^)^) return;
echo.
echo         // Get process information
echo         uint processId;
echo         GetWindowThreadProcessId(handle, out processId^);
echo         Process process = null;
echo         string processName = "Unknown";
echo         double cpuUsage = 0;
echo         long memoryUsage = 0;
echo.
echo         try
echo         {
echo             process = Process.GetProcessById((int^)processId^);
echo             processName = process.ProcessName;
echo             memoryUsage = process.WorkingSet64 / 1024 / 1024; // Convert to MB
echo         }
echo         catch { }
echo.
echo         // Get idle time
echo         int idleTime = GetIdleTime(^);
echo.
echo         // Take screenshot based on interval
echo         string screenshotPath = null;
echo         screenshotCounter++;
echo         if (screenshotCounter >= (screenshotInterval / monitoringInterval^)^)
echo         {
echo             screenshotPath = TakeScreenshot(^);
echo             screenshotCounter = 0; // Reset counter
echo             Console.WriteLine("📸 Screenshot captured at interval: " + screenshotInterval + "s"^);
echo         }

echo.
echo         // Get screen resolution
echo         string screenResolution = Screen.PrimaryScreen.Bounds.Width + "x" + Screen.PrimaryScreen.Bounds.Height;
echo.
echo         var logData = new
echo         {
echo             username = Environment.UserName,
echo             hostname = Environment.MachineName,
echo             window_title = windowTitle.ToString(^),
echo             process_name = processName,
echo             cpu_usage = cpuUsage,
echo             memory_usage = memoryUsage,
echo             screenshot_path = screenshotPath,
echo             screen_resolution = screenResolution,
echo             idle_time = idleTime,
echo             key_strokes = keyStrokes,
echo             mouse_clicks = mouseClicks
echo         };
echo.
echo         JavaScriptSerializer serializer = new JavaScriptSerializer(^);
echo         string json = serializer.Serialize(logData^);
echo.
echo         try
echo         {
echo             using (WebClient client = new WebClient(^)^)
echo             {
echo                 client.Headers[HttpRequestHeader.ContentType] = "application/json";
echo                 client.UploadString(serverUrl, json^);
echo                 Console.WriteLine("📊 " + DateTime.Now.ToString("HH:mm:ss"^) + " - " + windowTitle + " ^| " + processName + " ^| " + memoryUsage + "MB"^);
echo             }
echo         }
echo         catch (Exception ex^)
echo         {
echo             Console.WriteLine("❌ Failed to send log: " + ex.Message^);
echo         }
echo.
echo         // Reset counters
echo         keyStrokes = 0;
echo         mouseClicks = 0;
echo     }
echo.
echo     static string TakeScreenshot(^)
echo     {
echo         try
echo         {
echo             Rectangle bounds = Screen.PrimaryScreen.Bounds;
echo             using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height^)^)
echo             {
echo                 using (Graphics g = Graphics.FromImage(bitmap^)^)
echo                 {
echo                     g.CopyFromScreen(Point.Empty, Point.Empty, bounds.Size^);
echo                 }
echo.
echo                 string fileName = "screenshots/screenshot_" + DateTime.Now.ToString("yyyyMMdd_HHmmss"^) + ".png";
echo                 bitmap.Save(fileName, ImageFormat.Png^);
echo                 return fileName;
echo             }
echo         }
echo         catch
echo         {
echo             return null;
echo         }
echo     }
echo.
echo     static int GetIdleTime(^)
echo     {
echo         LASTINPUTINFO lastInputInfo = new LASTINPUTINFO(^);
echo         lastInputInfo.cbSize = (UInt32^)Marshal.SizeOf(lastInputInfo^);
echo         GetLastInputInfo(ref lastInputInfo^);
echo.
echo         return (int^)((Environment.TickCount - lastInputInfo.dwTime^) / 1000^);
echo     }
echo.
echo     static void LoadSettings(^)
echo     {
echo         try
echo         {
echo             using (WebClient client = new WebClient(^)^)
echo             {
echo                 string response = client.DownloadString(settingsUrl^);
echo                 // Simple parsing for screenshot_interval
echo                 if (response.Contains("screenshot_interval"^)^)
echo                 {
echo                     // Extract number after "screenshot_interval":
echo                     int start = response.IndexOf("screenshot_interval"^) + 20;
echo                     int end = response.IndexOf(",", start^);
echo                     if (end == -1^) end = response.IndexOf("}", start^);
echo                     string intervalStr = response.Substring(start, end - start^).Trim(^);
echo                     int interval;
echo                     if (int.TryParse(intervalStr, out interval^)^)
echo                     {
echo                         screenshotInterval = interval;
echo                         Console.WriteLine("📋 Screenshot interval updated to: " + screenshotInterval + " seconds"^);
echo                     }
echo                 }
echo             }
echo         }
echo         catch (Exception ex^)
echo         {
echo             Console.WriteLine("⚠️ Could not load settings: " + ex.Message^);
echo         }
echo     }
echo }
) > SystemMonitor.cs

REM Build using built-in Windows compiler
echo.
echo Building client...
%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe /reference:System.Web.Extensions.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll SystemMonitor.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Client built successfully!
    echo.
    echo To run the client:
    echo   cd client
    echo   SystemMonitor.exe
    echo.
) else (
    echo.
    echo ❌ Build failed. Trying 32-bit compiler...
    %WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe /reference:System.Web.Extensions.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll SystemMonitor.cs
    if !ERRORLEVEL! EQU 0 (
        echo ✅ Client built successfully with 32-bit compiler!
        echo.
        echo To run the client:
        echo   cd client
        echo   SystemMonitor.exe
    ) else (
        echo ❌ Build failed. .NET Framework might not be installed.
    )
)

echo ========================================
echo    Installation Complete
echo ========================================
pause