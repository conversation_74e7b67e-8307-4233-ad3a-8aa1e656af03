@echo off
echo ========================================
echo    Starting React Frontend Dashboard
echo ========================================
echo.

cd frontend

REM Check if Node.js is installed
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js not found. Please install Node.js first.
    echo Download from: https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo.
)

echo Starting React development server...
echo.
echo 🌐 Frontend dashboard will be available at: http://localhost:6500
echo 📊 Make sure backend is running on http://localhost:5500
echo ⚛️  React development server with hot reload
echo.
echo Press Ctrl+C to stop the frontend server
echo.

npm run dev

pause