# System Monitor - Enhanced

A comprehensive system monitoring solution with detailed logging and screenshot capabilities.

## 🚀 Quick Start

### One-Click Setup
```bash
run.bat
```
This will automatically:
- Install dependencies
- Build monitoring client
- Start backend server (port 5500)
- Start frontend dashboard (port 6500)
- Launch monitoring client
- Open dashboard in browser

### Manual Setup
1. **Backend**: `start-backend.bat` → http://localhost:5500
2. **Frontend**: `start-frontend.bat` → http://localhost:6500
3. **Client**: `client-installer.bat` then `cd client && SystemMonitor.exe`

## 📊 Enhanced Features

### System Monitoring
- **Window Activity** - Active window titles and process names
- **System Performance** - CPU usage and memory consumption
- **Screen Capture** - Automatic screenshots every 10 seconds
- **User Activity** - Idle time tracking
- **System Info** - Screen resolution and hostname

### Dashboard Features
- **Real-time Updates** - Auto-refresh every 5 seconds
- **Enhanced Logs** - Process info, system stats, screenshots
- **Search & Filter** - Find specific activities
- **Visual Stats** - CPU, memory, and activity indicators
- **Screenshot Viewer** - Click to view captured screens

## 🛠️ Requirements

- **Node.js** (for backend server)
- **PostgreSQL** (database storage)
- **.NET Framework 4.0+** (built into Windows)

## 📁 Project Structure

```
system-monitor/
├── backend/
│   ├── server.js          # Express API server (port 5500)
│   ├── package.json       # Backend dependencies
│   └── .env              # Database configuration
├── frontend/
│   └── index.html        # Enhanced dashboard (port 6500)
├── client/
│   ├── SystemMonitor.exe  # Built monitoring client
│   └── screenshots/       # Captured screenshots
├── run.bat               # One-click startup
├── stop.bat              # Stop all services
├── client-installer.bat  # Build monitoring client
├── start-backend.bat     # Start backend only
└── start-frontend.bat    # Start frontend only
```

## 🎯 What Gets Monitored

| Data Type | Description | Update Frequency |
|-----------|-------------|------------------|
| Window Title | Active application window | Every 10 seconds |
| Process Name | Running application process | Every 10 seconds |
| CPU Usage | Current CPU utilization | Every 10 seconds |
| Memory Usage | RAM consumption (MB) | Every 10 seconds |
| Screenshots | Desktop capture | Every 10 seconds |
| Screen Resolution | Display dimensions | Every 10 seconds |
| Idle Time | User inactivity duration | Every 10 seconds |
| User Info | Username and hostname | Every 10 seconds |

## 🔧 Configuration

### Database Settings (`backend/.env`)
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=system_monitor
DB_USER=postgres
DB_PASSWORD=root
PORT=5500
```

### Client Settings
- Screenshot storage: `client/screenshots/`
- Monitoring interval: 10 seconds
- Server endpoint: http://localhost:5500/api/logs

## 🌐 Access Points

- **Dashboard**: http://localhost:6500
- **API**: http://localhost:5500/api/logs
- **Screenshots**: http://localhost:5500/screenshots/
- **Health Check**: http://localhost:5500/health

## 🛑 Stop Services

```bash
stop.bat
```
Kills all Node.js, Python, and SystemMonitor processes.

## 🔒 Privacy & Security

- Screenshots stored locally in `client/screenshots/`
- Database contains only system performance data
- No personal files or content monitored
- All data stays on your local machine