import React from 'react';
import { Activity } from 'lucide-react';

const ProcessMonitor = ({ logs }) => {
  // Get unique processes with their latest data
  const processMap = new Map();
  
  logs.forEach(log => {
    if (log.process_name && !processMap.has(log.process_name)) {
      processMap.set(log.process_name, {
        name: log.process_name,
        memory: parseInt(log.memory_usage) || 0,
        cpu: parseFloat(log.cpu_usage) || 0,
        lastSeen: new Date(log.timestamp),
        windowTitle: log.window_title
      });
    }
  });

  const processes = Array.from(processMap.values())
    .sort((a, b) => b.memory - a.memory)
    .slice(0, 8);

  const getProcessColor = (memory) => {
    if (memory < 100) return 'bg-green-100 text-green-800';
    if (memory < 500) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const formatMemory = (mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Activity className="h-6 w-6 text-purple-500" />
          <h2 className="text-xl font-semibold text-gray-800">Active Processes</h2>
        </div>
        <div className="text-sm text-gray-500">
          {processes.length} processes
        </div>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        {processes.length > 0 ? (
          processes.map((process, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-800">{process.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${getProcessColor(process.memory)}`}>
                    {formatMemory(process.memory)}
                  </span>
                </div>
                <div className="text-sm text-gray-600 truncate mt-1">
                  {process.windowTitle}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Last seen: {process.lastSeen.toLocaleTimeString()}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-gray-800">
                  CPU: {process.cpu}%
                </div>
                <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                  <div 
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(process.cpu, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p>No active processes detected</p>
            <p className="text-sm">Start the monitoring client to see process data</p>
          </div>
        )}
      </div>

      {processes.length > 0 && (
        <div className="mt-4 pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-gray-800">
                {processes.reduce((sum, p) => sum + p.memory, 0)} MB
              </div>
              <div className="text-xs text-gray-500">Total Memory</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-800">
                {(processes.reduce((sum, p) => sum + p.cpu, 0) / processes.length).toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Avg CPU</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-800">{processes.length}</div>
              <div className="text-xs text-gray-500">Active</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessMonitor;