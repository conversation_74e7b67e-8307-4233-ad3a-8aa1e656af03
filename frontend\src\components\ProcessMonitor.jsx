import React from 'react';
import { Activity } from 'lucide-react';

const ProcessMonitor = ({ logs }) => {
  // Get unique processes with their latest data
  const processMap = new Map();
  
  logs.forEach(log => {
    if (log.process_name && !processMap.has(log.process_name)) {
      processMap.set(log.process_name, {
        name: log.process_name,
        memory: parseInt(log.memory_usage) || 0,
        cpu: parseFloat(log.cpu_usage) || 0,
        lastSeen: new Date(log.timestamp),
        windowTitle: log.window_title
      });
    }
  });

  const processes = Array.from(processMap.values())
    .sort((a, b) => b.memory - a.memory)
    .slice(0, 8);

  const getProcessColor = (memory) => {
    if (memory < 100) return 'bg-green-500/20 text-green-300 border border-green-500/30';
    if (memory < 500) return 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30';
    return 'bg-red-500/20 text-red-300 border border-red-500/30';
  };

  const formatMemory = (mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 p-6 shadow-xl">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-purple-500/20 p-2 rounded-lg border border-purple-500/30">
            <Activity className="h-6 w-6 text-purple-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">Active Processes</h2>
        </div>
        <div className="text-sm text-slate-300 bg-white/10 px-3 py-1 rounded-full border border-white/20">
          {processes.length} processes
        </div>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        {processes.length > 0 ? (
          processes.map((process, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-all duration-200">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <span className="font-medium text-white">{process.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${getProcessColor(process.memory)}`}>
                    {formatMemory(process.memory)}
                  </span>
                </div>
                <div className="text-sm text-slate-300 truncate mb-1">
                  {process.windowTitle}
                </div>
                <div className="text-xs text-slate-400">
                  Last seen: {process.lastSeen.toLocaleTimeString()}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-white mb-2">
                  CPU: {process.cpu}%
                </div>
                <div className="w-16 bg-white/10 rounded-full h-3 border border-white/20">
                  <div
                    className="bg-gradient-to-r from-purple-500 to-purple-400 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${Math.min(process.cpu, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-slate-400">
            <Activity className="h-12 w-12 mx-auto mb-2 text-slate-500" />
            <p className="text-white">No active processes detected</p>
            <p className="text-sm">Start the monitoring client to see process data</p>
          </div>
        )}
      </div>

      {processes.length > 0 && (
        <div className="mt-6 pt-4 border-t border-white/20">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-white/5 p-3 rounded-lg border border-white/10">
              <div className="text-lg font-semibold text-white">
                {processes.reduce((sum, p) => sum + p.memory, 0)} MB
              </div>
              <div className="text-xs text-slate-400">Total Memory</div>
            </div>
            <div className="bg-white/5 p-3 rounded-lg border border-white/10">
              <div className="text-lg font-semibold text-white">
                {(processes.reduce((sum, p) => sum + p.cpu, 0) / processes.length).toFixed(1)}%
              </div>
              <div className="text-xs text-slate-400">Avg CPU</div>
            </div>
            <div className="bg-white/5 p-3 rounded-lg border border-white/10">
              <div className="text-lg font-semibold text-white">{processes.length}</div>
              <div className="text-xs text-slate-400">Active</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessMonitor;