import React from 'react';
import { Monitor, RefreshCw, Wifi, WifiOff, <PERSON>, Moon } from 'lucide-react';

const Header = ({ isOnline, totalLogs, lastUpdate, onRefresh, isDarkMode, onToggleDarkMode }) => {
  return (
    <div className={`border-b p-4 mb-4 ${isDarkMode ? 'bg-white/10 backdrop-blur-sm border-white/20' : 'bg-white border-gray-200 shadow-sm'}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
            <Monitor className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>System Monitor</h1>
            <p className={`${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>Real-time system monitoring dashboard</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${isDarkMode ? 'bg-white/10 backdrop-blur-sm border-white/20' : 'bg-gray-100 border-gray-200'}`}>
            {isOnline ? (
              <Wifi className="h-5 w-5 text-green-400" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-400" />
            )}
            <span className={`text-sm font-medium ${isOnline ? 'text-green-300' : 'text-red-300'}`}>
              {isOnline ? 'Connected' : 'Offline'}
            </span>
          </div>

          <button
            onClick={onToggleDarkMode}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 border ${
              isDarkMode
                ? 'bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20'
                : 'bg-gray-100 border-gray-200 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            <span>{isDarkMode ? 'Light' : 'Dark'}</span>
          </button>

          <button
            onClick={onRefresh}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg border border-blue-500/30"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      <div className={`grid grid-cols-2 gap-4 mt-4 pt-4 border-t ${isDarkMode ? 'border-white/20' : 'border-gray-200'}`}>
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gradient-to-r from-blue-500/20 to-blue-600/20 backdrop-blur-sm border-blue-500/30' : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200'}`}>
          <div className={`text-2xl font-bold ${isDarkMode ? 'text-blue-300' : 'text-blue-600'}`}>{totalLogs.toLocaleString()}</div>
          <div className={`text-sm font-medium ${isDarkMode ? 'text-blue-200' : 'text-blue-500'}`}>Total Activity Records</div>
        </div>
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gradient-to-r from-green-500/20 to-green-600/20 backdrop-blur-sm border-green-500/30' : 'bg-gradient-to-r from-green-50 to-green-100 border-green-200'}`}>
          <div className={`text-2xl font-bold ${isDarkMode ? 'text-green-300' : 'text-green-600'}`}>{lastUpdate}</div>
          <div className={`text-sm font-medium ${isDarkMode ? 'text-green-200' : 'text-green-500'}`}>Last Update</div>
        </div>
      </div>
    </div>
  );
};

export default Header;