import React from 'react';
import { Monitor, RefreshCw, Wifi, WifiOff } from 'lucide-react';

const Header = ({ isOnline, totalLogs, lastUpdate, onRefresh }) => {
  return (
    <div className="bg-white/10 backdrop-blur-sm border-b border-white/20 p-4 mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
            <Monitor className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">System Monitor</h1>
            <p className="text-slate-300">Real-time system monitoring dashboard</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
            {isOnline ? (
              <Wifi className="h-5 w-5 text-green-400" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-400" />
            )}
            <span className={`text-sm font-medium ${isOnline ? 'text-green-300' : 'text-red-300'}`}>
              {isOnline ? 'Connected' : 'Offline'}
            </span>
          </div>

          <button
            onClick={onRefresh}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg border border-blue-500/30"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-white/20">
        <div className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 backdrop-blur-sm p-4 rounded-lg border border-blue-500/30">
          <div className="text-2xl font-bold text-blue-300">{totalLogs.toLocaleString()}</div>
          <div className="text-sm text-blue-200 font-medium">Total Activity Records</div>
        </div>
        <div className="bg-gradient-to-r from-green-500/20 to-green-600/20 backdrop-blur-sm p-4 rounded-lg border border-green-500/30">
          <div className="text-2xl font-bold text-green-300">{lastUpdate}</div>
          <div className="text-sm text-green-200 font-medium">Last Update</div>
        </div>
      </div>
    </div>
  );
};

export default Header;