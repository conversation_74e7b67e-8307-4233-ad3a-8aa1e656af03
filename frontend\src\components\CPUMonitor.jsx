import React from 'react';
import { Cpu } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const CPUMonitor = ({ logs }) => {
  const cpuData = logs.slice(0, 10).reverse().map((log, index) => ({
    time: new Date(log.timestamp).toLocaleTimeString(),
    cpu: parseFloat(log.cpu_usage) || 0
  }));

  const currentCPU = parseFloat(logs[0]?.cpu_usage) || 0;
  const avgCPU = logs.length > 0 ? 
    (logs.slice(0, 10).reduce((sum, log) => sum + (parseFloat(log.cpu_usage) || 0), 0) / Math.min(logs.length, 10)).toFixed(1) : 0;

  const getCPUColor = (usage) => {
    if (usage < 30) return 'text-green-400';
    if (usage < 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 p-6 shadow-xl">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-red-500/20 p-2 rounded-lg border border-red-500/30">
            <Cpu className="h-6 w-6 text-red-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">CPU Usage</h2>
        </div>
        <div className={`text-3xl font-bold ${getCPUColor(currentCPU)}`}>
          {currentCPU}%
        </div>
      </div>

      <div className="mb-6">
        <div className="flex justify-between text-sm text-slate-300 mb-3">
          <span>Current: {currentCPU}%</span>
          <span>Average: {avgCPU}%</span>
        </div>
        <div className="w-full bg-white/10 rounded-full h-4 border border-white/20">
          <div
            className={`h-4 rounded-full transition-all duration-500 ${
              currentCPU < 30 ? 'bg-gradient-to-r from-green-500 to-green-400' :
              currentCPU < 70 ? 'bg-gradient-to-r from-yellow-500 to-yellow-400' : 'bg-gradient-to-r from-red-500 to-red-400'
            }`}
            style={{ width: `${Math.min(currentCPU, 100)}%` }}
          ></div>
        </div>
      </div>

      <div className="h-40 bg-white/5 rounded-lg p-2 border border-white/10">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={cpuData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#ffffff20" />
            <XAxis dataKey="time" fontSize={10} stroke="#94a3b8" />
            <YAxis domain={[0, 100]} fontSize={10} stroke="#94a3b8" />
            <Tooltip
              contentStyle={{
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: '#fff'
              }}
            />
            <Line
              type="monotone"
              dataKey="cpu"
              stroke="#ef4444"
              strokeWidth={3}
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, fill: '#ef4444' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CPUMonitor;