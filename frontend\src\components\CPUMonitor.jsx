import React from 'react';
import { Cpu } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const CPUMonitor = ({ logs }) => {
  const cpuData = logs.slice(0, 10).reverse().map((log, index) => ({
    time: new Date(log.timestamp).toLocaleTimeString(),
    cpu: parseFloat(log.cpu_usage) || 0
  }));

  const currentCPU = parseFloat(logs[0]?.cpu_usage) || 0;
  const avgCPU = logs.length > 0 ? 
    (logs.slice(0, 10).reduce((sum, log) => sum + (parseFloat(log.cpu_usage) || 0), 0) / Math.min(logs.length, 10)).toFixed(1) : 0;

  const getCPUColor = (usage) => {
    if (usage < 30) return 'text-green-600';
    if (usage < 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Cpu className="h-6 w-6 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-800">CPU Usage</h2>
        </div>
        <div className={`text-2xl font-bold ${getCPUColor(currentCPU)}`}>
          {currentCPU}%
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Current: {currentCPU}%</span>
          <span>Average: {avgCPU}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${
              currentCPU < 30 ? 'bg-green-500' : 
              currentCPU < 70 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${Math.min(currentCPU, 100)}%` }}
          ></div>
        </div>
      </div>

      <div className="h-32">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={cpuData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" fontSize={10} />
            <YAxis domain={[0, 100]} fontSize={10} />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey="cpu" 
              stroke="#ef4444" 
              strokeWidth={2}
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CPUMonitor;