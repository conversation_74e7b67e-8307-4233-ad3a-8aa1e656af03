const express = require('express');
const { Client } = require('pg');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5500;

// Database setup
const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'system_monitor',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'root'
});

// Middleware
app.use(cors());
app.use(express.json());
app.use('/screenshots', express.static('../client/screenshots'));

// Initialize database
async function initDB() {
    try {
        await client.connect();
        // Drop existing table if it has wrong structure
        await client.query(`DROP TABLE IF EXISTS activity_logs`);

        // Create table with enhanced structure
        await client.query(`
            CREATE TABLE activity_logs (
                id SERIAL PRIMARY KEY,
                username VA<PERSON>HA<PERSON>(255),
                hostname VA<PERSON>HAR(255),
                window_title TEXT,
                process_name VA<PERSON>HA<PERSON>(255),
                cpu_usage DECIMAL(5,2),
                memory_usage BIGINT,
                screenshot_path TEXT,
                screen_resolution VARCHAR(50),
                idle_time INTEGER,
                key_strokes INTEGER,
                mouse_clicks INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ Database connected and initialized');
    } catch (err) {
        console.error('❌ Database error:', err.message);
    }
}

// API Routes
app.post('/api/logs', async (req, res) => {
    try {
        const {
            username,
            hostname,
            window_title,
            process_name,
            cpu_usage,
            memory_usage,
            screenshot_path,
            screen_resolution,
            idle_time,
            key_strokes,
            mouse_clicks
        } = req.body;

        await client.query(
            `INSERT INTO activity_logs 
            (username, hostname, window_title, process_name, cpu_usage, memory_usage, 
             screenshot_path, screen_resolution, idle_time, key_strokes, mouse_clicks) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
            [username, hostname, window_title, process_name, cpu_usage, memory_usage,
                screenshot_path, screen_resolution, idle_time, key_strokes, mouse_clicks]
        );

        res.json({ success: true });
        console.log(`📊 Enhanced Log: ${window_title} | CPU: ${cpu_usage}% | Memory: ${memory_usage}MB - ${username}@${hostname}`);
    } catch (err) {
        console.error('Error saving log:', err);
        res.status(500).json({ error: err.message });
    }
});

app.get('/api/logs', async (req, res) => {
    try {
        const result = await client.query(
            'SELECT * FROM activity_logs ORDER BY timestamp DESC LIMIT 100'
        );
        res.json(result.rows);
    } catch (err) {
        console.error('Error fetching logs:', err);
        res.status(500).json({ error: err.message });
    }
});

// Settings management
let systemSettings = {
    screenshot_interval: 60, // seconds (1 minute)
    monitoring_interval: 10, // seconds
    screenshot_enabled: true
};

app.get('/api/settings', (req, res) => {
    res.json(systemSettings);
});

app.post('/api/settings', (req, res) => {
    try {
        systemSettings = { ...systemSettings, ...req.body };
        res.json({ success: true, settings: systemSettings });
        console.log('📋 Settings updated:', systemSettings);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// Test screenshot endpoint
app.get('/api/test-screenshot', (req, res) => {
    const fs = require('fs');
    const path = require('path');
    
    try {
        const screenshotDir = path.join(__dirname, '../client/screenshots');
        const files = fs.readdirSync(screenshotDir);
        const pngFiles = files.filter(file => file.endsWith('.png'));
        
        if (pngFiles.length > 0) {
            const latestFile = pngFiles[pngFiles.length - 1];
            res.json({
                success: true,
                latestScreenshot: `screenshots/${latestFile}`,
                totalScreenshots: pngFiles.length,
                screenshotDir: screenshotDir,
                url: `http://localhost:5500/screenshots/${latestFile}`
            });
        } else {
            res.json({
                success: false,
                message: 'No screenshots found',
                screenshotDir: screenshotDir
            });
        }
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        message: 'SystemMonitor server is running',
        database: 'connected'
    });
});

// Start server
initDB().then(() => {
    app.listen(PORT, () => {
        console.log(`🚀 Backend server running on http://localhost:${PORT}`);
        console.log('🔌 API endpoints:');
        console.log(`   POST http://localhost:${PORT}/api/logs`);
        console.log(`   GET  http://localhost:${PORT}/api/logs`);
        console.log(`   GET  http://localhost:${PORT}/health`);
    });
});