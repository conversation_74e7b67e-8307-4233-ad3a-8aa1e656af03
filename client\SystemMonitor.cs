using System;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Web.Script.Serialization;
using System.Windows.Forms;

class SystemMonitor
{
    [DllImport("user32.dll")]
    static extern IntPtr GetForegroundWindow();

    [DllImport("user32.dll")]
    static extern int GetWindowText(IntPtr hWnd, StringBuilder text, int count);

    [DllImport("user32.dll")]
    static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);

    [DllImport("user32.dll")]
    static extern bool GetLastInputInfo(ref LASTINPUTINFO plii);

    [DllImport("user32.dll")]
    static extern bool SetProcessDPIAware();

    [DllImport("user32.dll")]
    static extern int GetSystemMetrics(int nIndex);

    // System metrics constants
    const int SM_CXSCREEN = 0;
    const int SM_CYSCREEN = 1;
    const int SM_XVIRTUALSCREEN = 76;
    const int SM_YVIRTUALSCREEN = 77;
    const int SM_CXVIRTUALSCREEN = 78;
    const int SM_CYVIRTUALSCREEN = 79;

    [StructLayout(LayoutKind.Sequential)]
    struct LASTINPUTINFO
    {
        public static readonly int SizeOf = Marshal.SizeOf(typeof(LASTINPUTINFO));
        [MarshalAs(UnmanagedType.U4)]
        public UInt32 cbSize;
        [MarshalAs(UnmanagedType.U4)]
        public UInt32 dwTime;
    }

    private static readonly string serverUrl = "http://localhost:5500/api/logs";
    private static readonly string settingsUrl = "http://localhost:5500/api/settings";
    private static int keyStrokes = 0;
    private static int mouseClicks = 0;
    private static int screenshotInterval = 60; // Default 1 minute
    private static int monitoringInterval = 10; // Default 10 seconds
    private static DateTime lastScreenshotTime = DateTime.MinValue;

    static void Main(string[] args)
    {
        // Set DPI awareness to get true screen dimensions
        SetProcessDPIAware();

        Console.WriteLine("🖥️  Enhanced System Monitor Started");
        Console.WriteLine("📊 Monitoring: Windows, CPU, Memory, Screenshots");
        Console.WriteLine("⏹️  Press Ctrl+C to stop");
        Console.WriteLine("================================");

        // Create screenshots directory
        if (!Directory.Exists("screenshots"))
            Directory.CreateDirectory("screenshots");

        // Get initial settings
        LoadSettings();
        int settingsRefreshCounter = 0;

        while (true)
        {
            try
            {
                // Refresh settings every 30 monitoring cycles (5 minutes if monitoring is 10s)
                settingsRefreshCounter++;
                if (settingsRefreshCounter >= 30)
                {
                    LoadSettings();
                    settingsRefreshCounter = 0;
                }

                LogSystemActivity();
                Thread.Sleep(monitoringInterval * 1000); // Use configurable interval
            }
            catch (Exception ex)
            {
                Console.WriteLine("❌ Error: " + ex.Message);
                Thread.Sleep(5000);
            }
        }
    }

    static void LogSystemActivity()
    {
        IntPtr handle = GetForegroundWindow();
        StringBuilder windowTitle = new StringBuilder(256);
        GetWindowText(handle, windowTitle, windowTitle.Capacity);

        if (string.IsNullOrEmpty(windowTitle.ToString())) return;

        // Get process information
        uint processId;
        GetWindowThreadProcessId(handle, out processId);
        Process process = null;
        string processName = "Unknown";
        double cpuUsage = 0;
        long memoryUsage = 0;

        try
        {
            process = Process.GetProcessById((int)processId);
            processName = process.ProcessName;
            memoryUsage = process.WorkingSet64 / 1024 / 1024; // Convert to MB
        }
        catch { }

        // Get idle time
        int idleTime = GetIdleTime();

        // Take screenshot based on interval
        string screenshotPath = null;
        DateTime now = DateTime.Now;
        if ((now - lastScreenshotTime).TotalSeconds >= screenshotInterval)
        {
            screenshotPath = TakeScreenshot();
            lastScreenshotTime = now;
            Console.WriteLine("📸 Screenshot captured at interval: " + screenshotInterval + "s");
        }

        // Get screen resolution (full virtual screen including all monitors)
        Rectangle virtualScreen = SystemInformation.VirtualScreen;
        string screenResolution = virtualScreen.Width + "x" + virtualScreen.Height;

        var logData = new
        {
            username = Environment.UserName,
            hostname = Environment.MachineName,
            window_title = windowTitle.ToString(),
            process_name = processName,
            cpu_usage = cpuUsage,
            memory_usage = memoryUsage,
            screenshot_path = screenshotPath,
            screen_resolution = screenResolution,
            idle_time = idleTime,
            key_strokes = keyStrokes,
            mouse_clicks = mouseClicks
        };

        JavaScriptSerializer serializer = new JavaScriptSerializer();
        string json = serializer.Serialize(logData);

        try
        {
            using (WebClient client = new WebClient())
            {
                client.Headers[HttpRequestHeader.ContentType] = "application/json";
                client.UploadString(serverUrl, json);
                Console.WriteLine("📊 " + DateTime.Now.ToString("HH:mm:ss") + " - " + windowTitle + " ^| " + processName + " ^| " + memoryUsage + "MB");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("❌ Failed to send log: " + ex.Message);
        }

        // Reset counters
        keyStrokes = 0;
        mouseClicks = 0;
    }

    static string TakeScreenshot()
    {
        try
        {
            // Get true screen dimensions using system metrics (DPI-aware)
            int x = GetSystemMetrics(SM_XVIRTUALSCREEN);
            int y = GetSystemMetrics(SM_YVIRTUALSCREEN);
            int width = GetSystemMetrics(SM_CXVIRTUALSCREEN);
            int height = GetSystemMetrics(SM_CYVIRTUALSCREEN);

            // Debug: Print all screen information
            Console.WriteLine("🔍 Debug: Screen Information:");
            Console.WriteLine("   SystemMetrics Virtual Screen: X=" + x + " Y=" + y + " W=" + width + " H=" + height);
            Console.WriteLine("   SystemInformation Virtual Screen: " + SystemInformation.VirtualScreen);
            Console.WriteLine("   Primary Screen: " + Screen.PrimaryScreen.Bounds);
            Console.WriteLine("   Number of screens: " + Screen.AllScreens.Length);

            foreach (Screen screen in Screen.AllScreens)
            {
                Console.WriteLine("   Screen: " + screen.Bounds + " Primary: " + screen.Primary);
            }

            // Use system metrics for accurate dimensions
            Rectangle bounds = new Rectangle(x, y, width, height);
            Console.WriteLine("   Using bounds: " + bounds);

            using (Bitmap bitmap = new Bitmap(width, height))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    // Copy from the virtual screen which includes all monitors
                    g.CopyFromScreen(x, y, 0, 0, new Size(width, height));
                }

                string fileName = "screenshots/screenshot_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".png";
                bitmap.Save(fileName, ImageFormat.Png);
                Console.WriteLine("📸 Screenshot saved: " + fileName + " (" + width + "x" + height + ")");
                return fileName;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("❌ Screenshot error: " + ex.Message);
            return null;
        }
    }

    static int GetIdleTime()
    {
        LASTINPUTINFO lastInputInfo = new LASTINPUTINFO();
        lastInputInfo.cbSize = (UInt32)Marshal.SizeOf(lastInputInfo);
        GetLastInputInfo(ref lastInputInfo);

        return (int)((Environment.TickCount - lastInputInfo.dwTime) / 1000);
    }

    static void LoadSettings()
    {
        try
        {
            using (WebClient client = new WebClient())
            {
                string response = client.DownloadString(settingsUrl);
                JavaScriptSerializer serializer = new JavaScriptSerializer();
                dynamic settings = serializer.DeserializeObject(response);

                if (settings.ContainsKey("screenshot_interval"))
                {
                    screenshotInterval = (int)settings["screenshot_interval"];
                    Console.WriteLine("📋 Screenshot interval updated to: " + screenshotInterval + " seconds");
                }

                if (settings.ContainsKey("monitoring_interval"))
                {
                    monitoringInterval = (int)settings["monitoring_interval"];
                    Console.WriteLine("📋 Monitoring interval updated to: " + monitoringInterval + " seconds");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("⚠️ Could not load settings: " + ex.Message);
        }
    }
}
