import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Header from './components/Header';
import CPUMonitor from './components/CPUMonitor';
import RAMMonitor from './components/RAMMonitor';
import HardDriveMonitor from './components/HardDriveMonitor';
import ProcessMonitor from './components/ProcessMonitor';
import ScreenshotViewer from './components/ScreenshotViewer';
import ActivityLogs from './components/ActivityLogs';
import Settings from './components/Settings';

const API_BASE = 'http://localhost:5500';

function App() {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(false);

  const fetchLogs = async () => {
    try {
      const response = await axios.get(`${API_BASE}/api/logs`);
      setLogs(response.data);
      setIsOnline(true);
      setError(null);
    } catch (err) {
      setError(err.message);
      setIsOnline(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
    const interval = setInterval(fetchLogs, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const latestLog = logs[0] || {};
  const recentLogs = logs.slice(0, 10);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white text-xl">Loading System Monitor...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="max-w-7xl mx-auto p-6">
        <Header
          isOnline={isOnline}
          totalLogs={logs.length}
          lastUpdate={new Date().toLocaleTimeString()}
          onRefresh={fetchLogs}
        />

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 text-red-700 px-6 py-4 rounded-r-lg mb-6 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">Connection Error</p>
                <p className="text-sm">{error}. Make sure the backend server is running on port 5500.</p>
              </div>
            </div>
          </div>
        )}

        {/* System Overview Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          <CPUMonitor logs={recentLogs} />
          <RAMMonitor logs={recentLogs} />
          <HardDriveMonitor logs={recentLogs} />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
          <div className="xl:col-span-2">
            <ProcessMonitor logs={recentLogs} />
          </div>
          <div>
            <ScreenshotViewer latestLog={latestLog} />
          </div>
        </div>

        {/* Settings and Configuration */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
          <Settings />
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">System Status</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Connection Status</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Total Records</span>
                <span className="text-sm font-medium text-slate-800">{logs.length.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Last Update</span>
                <span className="text-sm font-medium text-slate-800">{new Date().toLocaleTimeString()}</span>
              </div>
              {latestLog.hostname && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-600">Hostname</span>
                  <span className="text-sm font-medium text-slate-800">{latestLog.hostname}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Activity Logs */}
        <ActivityLogs logs={logs} />
      </div>
    </div>
  );
}

export default App;